import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';

interface BroadcastNotificationRequest {
  title: string;
  message: string;
  targetRole?: 'student' | 'merchant' | 'all';
}

/**
 * Send broadcast notification to all users
 * Only admins can call this function
 */
export const sendBroadcastNotification = functions.https.onCall(async (data: BroadcastNotificationRequest, context) => {
  // Verify authentication
  if (!context?.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const adminId = context.auth.uid;
  const { title, message, targetRole = 'all' } = data;

  // Validate input
  if (!title?.trim() || !message?.trim()) {
    throw new functions.https.HttpsError('invalid-argument', 'Title and message are required');
  }

  try {
    console.log('Starting broadcast notification...', { adminId, title, targetRole });

    // Get admin data and verify admin role
    const adminDoc = await admin.firestore().collection('users').doc(adminId).get();
    if (!adminDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Admin not found');
    }

    const adminData = adminDoc.data();
    if (!adminData || adminData.role !== 'admin') {
      throw new functions.https.HttpsError('permission-denied', 'Only admins can send broadcast notifications');
    }

    console.log('Admin verified:', { name: adminData.name, role: adminData.role });

    // Get all users based on target role
    let usersQuery: admin.firestore.Query = admin.firestore().collection('users');
    if (targetRole && targetRole !== 'all') {
      usersQuery = usersQuery.where('role', '==', targetRole);
    }

    const usersSnapshot = await usersQuery.get();
    console.log(`Found ${usersSnapshot.size} users to notify`);

    // Use batch writes to create notifications efficiently
    const batchSize = 500; // Firestore batch limit
    const batches: admin.firestore.WriteBatch[] = [];
    let currentBatch = admin.firestore().batch();
    let operationCount = 0;
    let notificationCount = 0;

    // Create notifications for all users in their individual subcollections
    usersSnapshot.forEach((userDoc) => {
      const userData = userDoc.data();
      if (userData.role !== 'admin') { // Don't send to other admins
        // Create notification in user's notifications subcollection
        const notificationRef = admin.firestore()
          .collection(`users/${userDoc.id}/notifications`)
          .doc();

        currentBatch.set(notificationRef, {
          type: 'admin_broadcast',
          senderId: adminId,
          senderName: adminData.name || 'Admin',
          title: `📢 ${title}`,
          message,
          actionRequired: false,
          expiresAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7 days
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          dismissed: false,
          read: false,
          icon: '📢',
          priority: 'normal'
        });

        operationCount++;
        notificationCount++;

        // If we've reached the batch size limit, start a new batch
        if (operationCount >= batchSize) {
          batches.push(currentBatch);
          currentBatch = admin.firestore().batch();
          operationCount = 0;
        }
      }
    });

    // Add the last batch if it has operations
    if (operationCount > 0) {
      batches.push(currentBatch);
    }

    console.log(`Committing ${batches.length} batches with ${notificationCount} notifications...`);

    // Commit all batches
    const batchPromises = batches.map(async (batch, index) => {
      console.log(`Committing batch ${index + 1}/${batches.length}`);
      return batch.commit();
    });

    await Promise.all(batchPromises);

    console.log('Broadcast notification sent successfully');

    // Log admin action
    await admin.firestore().collection('adminLogs').add({
      adminId,
      adminName: adminData.name || 'Admin',
      action: 'broadcast_notification',
      details: {
        title,
        message: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
        targetRole,
        recipientCount: notificationCount
      },
      timestamp: admin.firestore.FieldValue.serverTimestamp()
    });

    return {
      success: true,
      recipientCount: notificationCount,
      message: `Broadcast notification sent to ${notificationCount} users`
    };

  } catch (error) {
    console.error('Error sending broadcast notification:', error);
    
    // Log the error
    await admin.firestore().collection('adminLogs').add({
      adminId,
      action: 'broadcast_notification_error',
      error: error instanceof Error ? error.message : 'Unknown error',
      details: { title, message: message?.substring(0, 100), targetRole },
      timestamp: admin.firestore.FieldValue.serverTimestamp()
    }).catch(logError => {
      console.error('Failed to log error:', logError);
    });

    // Re-throw as HttpsError for proper client handling
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    
    throw new functions.https.HttpsError('internal', 'Failed to send broadcast notification');
  }
});

/**
 * Send individual notification to a specific user
 * Only admins can call this function
 */
export const sendUserNotification = functions.https.onCall(async (data: {
  userId: string;
  title: string;
  message: string;
  type?: 'warning' | 'message';
}, context) => {
  // Verify authentication
  if (!context?.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const adminId = context.auth.uid;
  const { userId, title, message, type = 'message' } = data;

  // Validate input
  if (!userId || !title?.trim() || !message?.trim()) {
    throw new functions.https.HttpsError('invalid-argument', 'User ID, title, and message are required');
  }

  try {
    // Get admin data and verify admin role
    const adminDoc = await admin.firestore().collection('users').doc(adminId).get();
    if (!adminDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Admin not found');
    }

    const adminData = adminDoc.data();
    if (!adminData || adminData.role !== 'admin') {
      throw new functions.https.HttpsError('permission-denied', 'Only admins can send notifications');
    }

    // Verify target user exists
    const userDoc = await admin.firestore().collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Target user not found');
    }

    // Create notification in user's notifications subcollection
    await admin.firestore()
      .collection(`users/${userId}/notifications`)
      .add({
        type: type === 'warning' ? 'admin_warning' : 'admin_message',
        senderId: adminId,
        senderName: adminData.name || 'Admin',
        title: type === 'warning' ? `⚠️ ${title}` : `📢 ${title}`,
        message,
        actionRequired: type === 'warning',
        expiresAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7 days
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        dismissed: false,
        read: false,
        icon: type === 'warning' ? '⚠️' : '📢',
        priority: type === 'warning' ? 'high' : 'normal'
      });

    // Log admin action
    await admin.firestore().collection('adminLogs').add({
      adminId,
      adminName: adminData.name || 'Admin',
      action: 'user_notification',
      details: {
        targetUserId: userId,
        title,
        message: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
        type
      },
      timestamp: admin.firestore.FieldValue.serverTimestamp()
    });

    return {
      success: true,
      message: 'Notification sent successfully'
    };

  } catch (error) {
    console.error('Error sending user notification:', error);
    
    // Re-throw as HttpsError for proper client handling
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    
    throw new functions.https.HttpsError('internal', 'Failed to send notification');
  }
});
